import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { models } from '../data/models'
import { IconArrowLeft } from '@tabler/icons-react'
import { UploadProfileImage, ProfileImagesList } from './components'

export default function ModelProfileImages() {
    const { modelId } = useParams({ from: '/_authenticated/models/profile-images/$modelId' })

    // Find the model by ID
    const model = models.find(m => m.id === modelId)

    // if (!model) {
    //     return (
    //         <div className="flex items-center justify-center h-64">
    //             <p className="text-muted-foreground">Model not found</p>
    //         </div>
    //     )
    // }

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
                    <Link to="/models" className="flex items-center gap-1 hover:text-gray-900">
                        <IconArrowLeft className="h-4 w-4" />
                        Models
                    </Link>
                    <span>/</span>
                    {/* <Link to="/models/profile/$modelId" params={{ modelId }} className="hover:text-gray-900">
                        {model?.profile}
                    </Link>
                    <span>/</span> */}
                    <span className="text-gray-900">Uploaded Images</span>
                </div>

                {/* Main Content */}
                <div className="space-y-6">
                    {/* Upload Section */}
                    <UploadProfileImage
                        modelId={modelId}
                        modelName={model?.profile}
                    />

                    {/* Profile Images List */}
                    <ProfileImagesList
                        modelId={modelId}
                        modelName={model?.profile}
                    />
                </div>
            </div>
        </div>
    )
}