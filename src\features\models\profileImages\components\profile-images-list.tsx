import { IconDotsVertical, IconStar, IconTrash } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'
import {
    setDefaultModelImageApi,
    deleteModelImageApi,
    getModelProfileImages
} from '../../api'
import { S3_BASE_URL } from '@/features/members/utils/utilities'

// Interface for profile images
interface ProfileImage {
    id: string
    filename: string
    url: string
    isDefault: boolean
    uploadedAt: string
}

interface ProfileImagesListProps {
    modelId: string
    modelName?: string
}

export default function ProfileImagesList({ modelId, modelName }: ProfileImagesListProps) {
    const queryClient = useQueryClient()

    // API queries and mutations
    const { data: profileImages = [], isLoading } = getModelProfileImages(modelId) as { data: ProfileImage[], isLoading: boolean }
    const { mutateAsync: setDefaultModelImageMutation } = setDefaultModelImageApi()
    const { mutateAsync: deleteModelImageMutation } = deleteModelImageApi()

    const handleSetAsDefault = async (imageId: string) => {
        try {
            await setDefaultModelImageMutation({
                modelId: modelId,
                imageId: imageId
            })
            toast.success("Set as default cover successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-profile-images'] })

        } catch (err) {
            toast.error("Failed to set as default cover")
        }
    }

    const handleDeleteImage = async (imageId: string) => {
        try {
            await deleteModelImageMutation({
                modelId: modelId,
                imageId: imageId
            })
            toast.success("Image deleted successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-profile-images'] })

        } catch (err) {
            toast.error("Failed to delete image")
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">Profile Images</CardTitle>
                {/* <p className="text-sm text-muted-foreground">
                    Manage {modelName}'s profile images
                </p> */}
            </CardHeader>
            <CardContent>
                {isLoading ? (
                    <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                        <p className="text-muted-foreground mt-2">Loading images...</p>
                    </div>
                ) : profileImages.length === 0 ? (
                    <div className="text-center py-8">
                        <p className="text-muted-foreground">No profile images uploaded yet</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {profileImages.map((image) => (
                            <div key={image.id} className="relative group">
                                {/* Image Container */}
                                <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
                                    <img
                                        src={image.url || S3_BASE_URL + image.filename}
                                        alt={`Profile ${image.id}`}
                                        className="w-full h-full object-cover"
                                        loading="lazy"
                                    />

                                    {/* Default Badge */}
                                    {image.isDefault && (
                                        <div className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded flex items-center gap-1">
                                            <IconStar className="h-3 w-3" />
                                            Default
                                        </div>
                                    )}

                                    {/* Menu Button */}
                                    <div className="absolute top-2 right-2">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button
                                                    variant="secondary"
                                                    size="icon"
                                                    className="h-8 w-8 bg-white/80 hover:bg-white/90 backdrop-blur-sm"
                                                >
                                                    <IconDotsVertical className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end" className="w-48">
                                                {!image.isDefault && (
                                                    <>
                                                        <DropdownMenuItem
                                                            onClick={() => handleSetAsDefault(image.id)}
                                                            className="flex items-center gap-2"
                                                        >
                                                            <IconStar className="h-4 w-4" />
                                                            Set as Default Cover
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                    </>
                                                )}
                                                <DropdownMenuItem
                                                    onClick={() => handleDeleteImage(image.id)}
                                                    className="flex items-center gap-2 text-destructive focus:text-destructive"
                                                >
                                                    <IconTrash className="h-4 w-4" />
                                                    Delete Image
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </div>

                                {/* Image Info */}
                                <div className="mt-2 text-xs text-muted-foreground">
                                    <p className="truncate">{image.filename}</p>
                                    <p>{new Date(image.uploadedAt).toLocaleDateString()}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
